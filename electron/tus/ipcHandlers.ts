import { ipc<PERSON>ain } from "electron";
import type { TusUploadManager } from "./uploadManager";
import type { TusUploadConfig, ApiResponse } from "./types";

/**
 * 注册所有 TUS 相关的 IPC 处理器
 */
export function registerTusIpcHandlers(uploadManager: TusUploadManager) {
  // 创建上传任务
  ipcMain.handle("tus-create-upload", async (_event, filePath: string, metadata?: Record<string, string>): Promise<ApiResponse> => {
    try {
      const taskId = await uploadManager.createUploadTask(filePath, metadata);
      return { success: true, taskId };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 开始上传
  ipcMain.handle("tus-start-upload", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.startUpload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 暂停上传
  ipcMain.handle("tus-pause-upload", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.pauseUpload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 恢复上传
  ipcMain.handle("tus-resume-upload", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.resumeUpload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 取消上传
  ipcMain.handle("tus-cancel-upload", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.cancelUpload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 重试上传
  ipcMain.handle("tus-retry-upload", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.retryUpload(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 删除任务
  ipcMain.handle("tus-delete-task", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      await uploadManager.deleteTask(taskId);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取所有任务
  ipcMain.handle("tus-get-all-tasks", async (_event): Promise<ApiResponse> => {
    try {
      const tasks = uploadManager.getAllTasks();
      return { success: true, tasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取指定任务
  ipcMain.handle("tus-get-task", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      const task = uploadManager.getTask(taskId);
      return { success: true, task };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取活跃任务
  ipcMain.handle("tus-get-active-tasks", async (_event): Promise<ApiResponse> => {
    try {
      const tasks = uploadManager.getActiveTasks();
      return { success: true, tasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取任务的上传URL
  ipcMain.handle("tus-get-task-upload-url", async (_event, taskId: string): Promise<ApiResponse> => {
    try {
      const uploadUrl = uploadManager.getTaskUploadUrl(taskId);
      return { success: true, data: { uploadUrl } };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 更新配置
  ipcMain.handle("tus-update-config", async (_event, config: Partial<TusUploadConfig>): Promise<ApiResponse> => {
    try {
      uploadManager.updateConfig(config);
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 清理已完成的任务
  ipcMain.handle("tus-clear-completed-tasks", async (_event): Promise<ApiResponse> => {
    try {
      uploadManager.clearCompletedTasks();
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 清空所有任务
  ipcMain.handle("tus-clear-all-tasks", async (_event): Promise<ApiResponse> => {
    try {
      await uploadManager.clearAllTasks();
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 从 File 对象创建上传任务（将文件内容保存为临时文件）
  ipcMain.handle(
    "tus-create-upload-from-file",
    async (
      _event,
      fileData: {
        name: string;
        content: ArrayBuffer;
        metadata?: Record<string, string>;
      }
    ): Promise<ApiResponse> => {
      try {
        const path = await import("path");
        const fs = await import("fs/promises");
        const os = await import("os");

        // 处理文件名，替换路径分隔符并确保文件名安全
        const safeName = fileData.name.replace(/[/\\:*?"<>|]/g, "_");

        // 创建临时文件
        const tempDir = os.tmpdir();
        const tempFileName = `upload_${Date.now()}_${safeName}`;
        const tempFilePath = path.join(tempDir, tempFileName);

        // 写入文件内容
        await fs.writeFile(tempFilePath, Buffer.from(fileData.content));

        // 创建上传任务，保持原始文件名信息在元数据中
        const enhancedMetadata = {
          ...fileData.metadata,
          originalName: fileData.name, // 保留原始文件名（可能包含路径）
          tempFilePath: tempFilePath, // 记录临时文件路径
        };

        const taskId = await uploadManager.createUploadTask(tempFilePath, enhancedMetadata);

        return { success: true, taskId };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );

  // 通过文件选择对话框创建上传任务（避免通过 IPC 传递大文件内容）
  ipcMain.handle("tus-create-upload-from-dialog", async (_event): Promise<ApiResponse> => {
    try {
      const { dialog } = await import("electron");

      const result = await dialog.showOpenDialog({
        properties: ["openFile", "multiSelections"],
        title: "选择要上传的文件",
        filters: [
          { name: "所有文件", extensions: ["*"] },
          { name: "图片", extensions: ["jpg", "jpeg", "png", "gif", "bmp", "webp"] },
          { name: "视频", extensions: ["mp4", "avi", "mov", "wmv", "flv", "webm"] },
          { name: "文档", extensions: ["pdf", "doc", "docx", "txt", "rtf"] },
        ],
      });

      if (result.canceled || result.filePaths.length === 0) {
        return { success: false, error: "用户取消了文件选择" };
      }

      // 创建多个上传任务
      const taskIds: string[] = [];

      for (const filePath of result.filePaths) {
        const taskId = await uploadManager.createUploadTask(filePath);
        taskIds.push(taskId);
      }

      return {
        success: true,
        data: { taskIds, count: taskIds.length },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 从文件路径列表创建批量上传任务（推荐用于大文件）
  ipcMain.handle("tus-create-uploads-from-paths", async (_event, filePaths: string[], metadata?: Record<string, string>): Promise<ApiResponse> => {
    try {
      const taskIds: string[] = [];

      for (const filePath of filePaths) {
        const taskId = await uploadManager.createUploadTask(filePath, metadata);
        taskIds.push(taskId);
      }

      return {
        success: true,
        data: { taskIds, count: taskIds.length },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 获取文件信息（不读取文件内容，仅获取基本信息）
  ipcMain.handle("tus-get-file-info", async (_event, filePath: string): Promise<ApiResponse> => {
    try {
      const fs = await import("fs/promises");
      const path = await import("path");

      const stats = await fs.stat(filePath);
      const fileName = path.basename(filePath);

      return {
        success: true,
        data: {
          path: filePath,
          name: fileName,
          size: stats.size,
          isFile: stats.isFile(),
          isDirectory: stats.isDirectory(),
          lastModified: stats.mtime.getTime(),
        },
      };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 智能打包上传（集成压缩和上传）
  ipcMain.handle(
    "tus-smart-pack-upload",
    async (
      _event,
      filePaths: string[],
      options?: {
        threshold?: number;
        archiveName?: string;
        metadata?: Record<string, string>;
        skipConfirmation?: boolean;
      }
    ): Promise<ApiResponse> => {
      try {
        // 这里会在后续实现中调用uploadManager的智能打包方法
        // 目前先返回一个占位符响应
        return {
          success: true,
          data: {
            message: "智能打包功能正在开发中",
            filePaths,
            options,
          },
        };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );
}

/**
 * 移除所有 TUS 相关的 IPC 处理器
 */
export function unregisterTusIpcHandlers() {
  const handlers = [
    "tus-create-upload",
    "tus-create-upload-from-file",
    "tus-start-upload",
    "tus-pause-upload",
    "tus-resume-upload",
    "tus-cancel-upload",
    "tus-retry-upload",
    "tus-delete-task",
    "tus-get-all-tasks",
    "tus-get-task",
    "tus-get-active-tasks",
    "tus-update-config",
    "tus-clear-completed-tasks",
    "tus-clear-all-tasks",
    "tus-create-upload-from-dialog",
    "tus-create-uploads-from-paths",
    "tus-get-file-info",
    "tus-smart-pack-upload",
  ];

  handlers.forEach((handler) => {
    ipcMain.removeHandler(handler);
  });
}
